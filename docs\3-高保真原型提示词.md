请基于[2-详细功能需求文档​](2-详细功能需求文档​.md)，生成符合微信小程序规范的高保真原型设计，需包含核心界面的交互逻辑、UI设计说明及原型代码（可选HTML/CSS示例）。  

#### **需求文档核心内容**  
**产品定位**：面向3-6岁幼儿家长的离线识字复习工具，核心功能为同步幼儿园字词、按艾宾浩斯曲线规划复习，强调家长陪伴、无游戏化设计，数据仅本地存储。  

**关键功能模块**：  
1. **新手引导页**：需说明开发目的（助力亲子高效识字）、操作指引，设置“我已了解”确认按钮。  
2. **当日字词复习**：  
   - 大字显示汉字（楷体，全屏单字），家长可标记“已掌握/需复习”。  
   - 支持手动录入或批量导入字词，本地记录复习进度。  
3. **历史字词复习**：  
   - 按艾宾浩斯曲线自动生成复习计划（天/周/月周期），每次5-10分钟任务。  
   - 显示正确率及薄弱字词，支持手动调整复习频次。  
4. **家长管理**：  
   - 幼儿信息设置（生日/班级，自动计算年龄），多账号管理。  
   - 字词库编辑、本地学习记录导出。  
5. **设置页**：底部小字提示“数据仅本地存储，删除后无法恢复”。  

**设计规范**：  
- 界面极简，低饱和度护眼色调（如浅绿/淡蓝为主色），无多余装饰元素。  
- 遵循《微信小程序设计指南》，使用官方图标库（如WeUI），适配iPhone 15 Pro（390x844px）。  
- 交互逻辑符合微信用户习惯，如左滑返回、底部Tab栏（首页/复习/我的）。  

#### **输出要求**  
1. **原型结构说明**：  
   - 列出核心界面（新手引导页、首页、复习详情页、家长中心、设置页）的层级关系。  
   - 标注每个界面的关键交互点（如按钮点击逻辑、数据加载状态）。  

2. **高保真UI设计说明**：  
   - 为每个界面提供视觉设计描述（布局、色彩、字体规格），示例：  
     - **首页标题**：黑体32pt加粗，居中，距顶部40px。  
     - **复习按钮**：圆角矩形，绿色#4CAF50，尺寸80x36px，点击时阴影加深。  
   - 说明如何体现“离线工具”特性（如无网络状态提示、本地数据加载图标）。  

3. **交互逻辑文档**：  
   - 描述从新手引导到复习流程的完整用户路径（如：引导页→首页→当日复习→标记完成→生成记录）。  
   - 说明艾宾浩斯算法在界面中的呈现方式（如复习计划日历、进度条）。  

4. **原型代码示例（可选）**：  
   - 用HTML/CSS/JS或微信小程序原生WXML/WXSS实现1-2个核心界面（如首页、复习页），包含动态数据绑定示例（如年龄计算、复习进度更新）。  
   - 示例代码需包含注释，引用WeUI组件库。  

#### **补充说明**  
- 数据存储风险提示需在设置页及首次操作时明显但不干扰主流程。  
- 界面需体现“高效工具”定位，减少层级跳转，核心功能一步直达。  